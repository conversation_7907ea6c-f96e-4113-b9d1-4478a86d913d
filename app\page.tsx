'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import {
  Store,
  Users,
  CreditCard,
  BarChart3,
  Download,
  Upload,
  CheckCircle,
  Star,
  ArrowRight,
  Menu,
  X,
  Phone,
  Mail,
  User
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

export default function LandingPage() {
  const router = useRouter();
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [formData, setFormData] = useState({
    name: '',
    phone: '',
    email: ''
  });

  const handleGetStarted = () => {
    router.push('/register');
  };

  const handleLogin = () => {
    router.push('/login');
  };

  const handleFormSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // For now, redirect to register with pre-filled data
    const params = new URLSearchParams({
      name: formData.name,
      phone: formData.phone,
      email: formData.email
    });
    router.push(`/register?${params.toString()}`);
  };

  return (
    <div className="min-h-screen bg-white">
      {/* Navigation */}
      <nav className="bg-white border-b border-gray-200 sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            {/* Logo */}
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-gradient-to-br from-green-600 to-red-600 rounded-lg flex items-center justify-center">
                <Store className="w-6 h-6 text-white" />
              </div>
              <div>
                <h1 className="text-xl font-bold text-gray-900">CRM الحانوت</h1>
                <p className="text-xs text-gray-500 hidden sm:block">Mini-Market Manager</p>
              </div>
            </div>

            {/* Desktop Navigation */}
            <div className="hidden md:flex items-center gap-4">
              <Button variant="ghost" onClick={handleLogin}>
                دخول / Connexion
              </Button>
              <Button onClick={handleGetStarted} className="bg-green-600 hover:bg-green-700">
                جرب مجانا / Essayer gratuitement
              </Button>
            </div>

            {/* Mobile menu button */}
            <div className="md:hidden">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsMenuOpen(!isMenuOpen)}
              >
                {isMenuOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
              </Button>
            </div>
          </div>

          {/* Mobile Navigation */}
          {isMenuOpen && (
            <div className="md:hidden border-t border-gray-200 py-4 space-y-2">
              <Button variant="ghost" className="w-full justify-start" onClick={handleLogin}>
                دخول / Connexion
              </Button>
              <Button className="w-full bg-green-600 hover:bg-green-700" onClick={handleGetStarted}>
                جرب مجانا / Essayer gratuitement
              </Button>
            </div>
          )}
        </div>
      </nav>

      {/* Hero Section */}
      <section className="bg-gradient-to-br from-green-50 via-white to-red-50 py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center space-y-8">
            {/* Main Headlines */}
            <div className="space-y-4">
              <h1 className="text-4xl md:text-6xl font-bold text-gray-900 leading-tight">
                <span className="block">دبر حانوتك بسهولة</span>
                <span className="block text-3xl md:text-5xl text-green-600 mt-2">
                  Gérez votre commerce facilement
                </span>
              </h1>

              <p className="text-xl md:text-2xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
                <span className="block mb-2">نظام إدارة مبسط للمحلات التجارية المغربية</span>
                <span className="block">Système de gestion simple pour les commerces marocains</span>
              </p>
            </div>

            {/* Value Proposition */}
            <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 md:p-8 max-w-4xl mx-auto border border-gray-200 shadow-lg">
              <div className="grid md:grid-cols-3 gap-6 text-center">
                <div className="space-y-2">
                  <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto">
                    <Users className="w-6 h-6 text-green-600" />
                  </div>
                  <h3 className="font-semibold text-gray-900">إدارة الزبائن</h3>
                  <p className="text-sm text-gray-600">Gestion clients</p>
                </div>
                <div className="space-y-2">
                  <div className="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mx-auto">
                    <CreditCard className="w-6 h-6 text-red-600" />
                  </div>
                  <h3 className="font-semibold text-gray-900">تتبع الديون</h3>
                  <p className="text-sm text-gray-600">Suivi des crédits</p>
                </div>
                <div className="space-y-2">
                  <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto">
                    <BarChart3 className="w-6 h-6 text-blue-600" />
                  </div>
                  <h3 className="font-semibold text-gray-900">إحصائيات المبيعات</h3>
                  <p className="text-sm text-gray-600">Statistiques ventes</p>
                </div>
              </div>
            </div>

            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <Button
                size="lg"
                className="bg-green-600 hover:bg-green-700 text-white px-8 py-4 text-lg font-semibold rounded-xl shadow-lg hover:shadow-xl transition-all duration-200"
                onClick={handleGetStarted}
              >
                جرب مجانا / Essayer gratuitement
                <ArrowRight className="ml-2 h-5 w-5" />
              </Button>
              <Button
                variant="outline"
                size="lg"
                className="border-2 border-gray-300 px-8 py-4 text-lg font-semibold rounded-xl hover:bg-gray-50 transition-all duration-200"
                onClick={handleLogin}
              >
                دخول / Connexion
              </Button>
            </div>

            {/* Trust Indicators */}
            <div className="flex flex-wrap justify-center items-center gap-6 text-sm text-gray-500 pt-8">
              <div className="flex items-center gap-2">
                <CheckCircle className="w-4 h-4 text-green-500" />
                <span>مجاني تماما / 100% Gratuit</span>
              </div>
              <div className="flex items-center gap-2">
                <CheckCircle className="w-4 h-4 text-green-500" />
                <span>سهل الاستخدام / Facile à utiliser</span>
              </div>
              <div className="flex items-center gap-2">
                <CheckCircle className="w-4 h-4 text-green-500" />
                <span>باللغة العربية / En français</span>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              <span className="block">مميزات النظام</span>
              <span className="block text-2xl md:text-3xl text-gray-600 mt-2">
                Fonctionnalités principales
              </span>
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              كل ما تحتاجه لإدارة محلك التجاري في مكان واحد
              <br />
              Tout ce dont vous avez besoin pour gérer votre commerce
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {/* Feature 1 */}
            <Card className="border-0 shadow-lg hover:shadow-xl transition-all duration-300 bg-white">
              <CardContent className="p-6 text-center space-y-4">
                <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl flex items-center justify-center mx-auto">
                  <Users className="w-8 h-8 text-white" />
                </div>
                <div>
                  <h3 className="text-xl font-bold text-gray-900 mb-2">سير الكليان ديالك بلا تعب</h3>
                  <p className="text-gray-600 font-medium mb-3">Gérer vos clients facilement</p>
                  <p className="text-sm text-gray-500 leading-relaxed">
                    احفظ معلومات الزبائن، تتبع مشترياتهم، وإدارة الديون بكل سهولة
                  </p>
                </div>
              </CardContent>
            </Card>

            {/* Feature 2 */}
            <Card className="border-0 shadow-lg hover:shadow-xl transition-all duration-300 bg-white">
              <CardContent className="p-6 text-center space-y-4">
                <div className="w-16 h-16 bg-gradient-to-br from-red-500 to-red-600 rounded-2xl flex items-center justify-center mx-auto">
                  <CreditCard className="w-8 h-8 text-white" />
                </div>
                <div>
                  <h3 className="text-xl font-bold text-gray-900 mb-2">تبع الكريديات والفلوس</h3>
                  <p className="text-gray-600 font-medium mb-3">Suivi des paiements et dettes</p>
                  <p className="text-sm text-gray-500 leading-relaxed">
                    راقب الديون المستحقة، سجل المدفوعات، واحصل على تقارير مالية دقيقة
                  </p>
                </div>
              </CardContent>
            </Card>

            {/* Feature 3 */}
            <Card className="border-0 shadow-lg hover:shadow-xl transition-all duration-300 bg-white">
              <CardContent className="p-6 text-center space-y-4">
                <div className="w-16 h-16 bg-gradient-to-br from-green-500 to-green-600 rounded-2xl flex items-center justify-center mx-auto">
                  <BarChart3 className="w-8 h-8 text-white" />
                </div>
                <div>
                  <h3 className="text-xl font-bold text-gray-900 mb-2">إحصائيات المنتوجات والمبيعات</h3>
                  <p className="text-gray-600 font-medium mb-3">Statistiques des produits et ventes</p>
                  <p className="text-sm text-gray-500 leading-relaxed">
                    اعرف أكثر المنتوجات مبيعا، تتبع الأرباح، وخذ قرارات ذكية لتجارتك
                  </p>
                </div>
              </CardContent>
            </Card>

            {/* Feature 4 */}
            <Card className="border-0 shadow-lg hover:shadow-xl transition-all duration-300 bg-white">
              <CardContent className="p-6 text-center space-y-4">
                <div className="w-16 h-16 bg-gradient-to-br from-purple-500 to-purple-600 rounded-2xl flex items-center justify-center mx-auto">
                  <div className="flex items-center gap-1">
                    <Upload className="w-4 h-4 text-white" />
                    <Download className="w-4 h-4 text-white" />
                  </div>
                </div>
                <div>
                  <h3 className="text-xl font-bold text-gray-900 mb-2">دخل وخرج البيانات بسهولة</h3>
                  <p className="text-gray-600 font-medium mb-3">Exporter/Importer vos données CSV/TXT</p>
                  <p className="text-sm text-gray-500 leading-relaxed">
                    استورد قوائم الزبائن والمنتوجات، وصدر التقارير بصيغ مختلفة
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Additional Benefits */}
          <div className="mt-16 bg-white rounded-2xl p-8 shadow-lg">
            <div className="grid md:grid-cols-3 gap-8 text-center">
              <div className="space-y-3">
                <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto">
                  <CheckCircle className="w-6 h-6 text-green-600" />
                </div>
                <h4 className="font-semibold text-gray-900">مجاني 100%</h4>
                <p className="text-sm text-gray-600">Complètement gratuit</p>
              </div>
              <div className="space-y-3">
                <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto">
                  <CheckCircle className="w-6 h-6 text-blue-600" />
                </div>
                <h4 className="font-semibold text-gray-900">يعمل على الهاتف</h4>
                <p className="text-sm text-gray-600">Compatible mobile</p>
              </div>
              <div className="space-y-3">
                <div className="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mx-auto">
                  <CheckCircle className="w-6 h-6 text-red-600" />
                </div>
                <h4 className="font-semibold text-gray-900">بدون تعقيدات</h4>
                <p className="text-sm text-gray-600">Interface simple</p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Screenshots Section */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              <span className="block">شوف النظام كيف خدام</span>
              <span className="block text-2xl md:text-3xl text-gray-600 mt-2">
                Découvrez l'interface
              </span>
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              واجهة بسيطة ومفهومة، مصممة خصيصا للتجار المغاربة
              <br />
              Interface simple et intuitive, conçue pour les commerçants marocains
            </p>
          </div>

          <div className="grid lg:grid-cols-3 gap-8">
            {/* Screenshot 1 - Dashboard */}
            <div className="space-y-4">
              <div className="bg-gradient-to-br from-blue-50 to-blue-100 rounded-2xl p-8 aspect-[4/3] flex items-center justify-center border border-blue-200">
                <div className="text-center space-y-4">
                  <div className="w-16 h-16 bg-blue-600 rounded-2xl flex items-center justify-center mx-auto">
                    <BarChart3 className="w-8 h-8 text-white" />
                  </div>
                  <div className="space-y-2">
                    <div className="h-3 bg-blue-300 rounded w-24 mx-auto"></div>
                    <div className="h-2 bg-blue-200 rounded w-16 mx-auto"></div>
                  </div>
                </div>
              </div>
              <div className="text-center">
                <h3 className="text-xl font-bold text-gray-900 mb-2">لوحة التحكم</h3>
                <p className="text-gray-600 font-medium mb-2">Tableau de bord</p>
                <p className="text-sm text-gray-500">
                  شوف مبيعات اليوم، الأرباح، والمنتوجات الناقصة في نظرة واحدة
                </p>
              </div>
            </div>

            {/* Screenshot 2 - Customer Management */}
            <div className="space-y-4">
              <div className="bg-gradient-to-br from-green-50 to-green-100 rounded-2xl p-8 aspect-[4/3] flex items-center justify-center border border-green-200">
                <div className="text-center space-y-4">
                  <div className="w-16 h-16 bg-green-600 rounded-2xl flex items-center justify-center mx-auto">
                    <Users className="w-8 h-8 text-white" />
                  </div>
                  <div className="space-y-2">
                    <div className="h-3 bg-green-300 rounded w-28 mx-auto"></div>
                    <div className="h-2 bg-green-200 rounded w-20 mx-auto"></div>
                    <div className="h-2 bg-green-200 rounded w-16 mx-auto"></div>
                  </div>
                </div>
              </div>
              <div className="text-center">
                <h3 className="text-xl font-bold text-gray-900 mb-2">إدارة الزبائن</h3>
                <p className="text-gray-600 font-medium mb-2">Gestion des clients</p>
                <p className="text-sm text-gray-500">
                  احفظ معلومات الزبائن، تتبع مشترياتهم وديونهم بكل سهولة
                </p>
              </div>
            </div>

            {/* Screenshot 3 - Sales Tracking */}
            <div className="space-y-4">
              <div className="bg-gradient-to-br from-red-50 to-red-100 rounded-2xl p-8 aspect-[4/3] flex items-center justify-center border border-red-200">
                <div className="text-center space-y-4">
                  <div className="w-16 h-16 bg-red-600 rounded-2xl flex items-center justify-center mx-auto">
                    <CreditCard className="w-8 h-8 text-white" />
                  </div>
                  <div className="space-y-2">
                    <div className="h-3 bg-red-300 rounded w-32 mx-auto"></div>
                    <div className="h-2 bg-red-200 rounded w-24 mx-auto"></div>
                    <div className="h-2 bg-red-200 rounded w-20 mx-auto"></div>
                  </div>
                </div>
              </div>
              <div className="text-center">
                <h3 className="text-xl font-bold text-gray-900 mb-2">تتبع المبيعات</h3>
                <p className="text-gray-600 font-medium mb-2">Suivi des ventes</p>
                <p className="text-sm text-gray-500">
                  سجل المبيعات، راقب الديون، واحصل على تقارير مفصلة
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section className="py-20 bg-gradient-to-br from-gray-50 to-gray-100">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              <span className="block">شنو كايقولو التجار</span>
              <span className="block text-2xl md:text-3xl text-gray-600 mt-2">
                Ce que disent nos utilisateurs
              </span>
            </h2>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            {/* Testimonial 1 */}
            <Card className="border-0 shadow-lg bg-white">
              <CardContent className="p-6">
                <div className="flex items-center gap-1 mb-4">
                  {[...Array(5)].map((_, i) => (
                    <Star key={i} className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                  ))}
                </div>
                <p className="text-gray-700 mb-4 leading-relaxed">
                  "النظام سهل بزاف وكايعاونني نتبع الزبائن والديون ديالهم. دابا ولا عندي تنظيم أحسن في الحانوت."
                </p>
                <p className="text-sm text-gray-500 mb-4">
                  "Le système est très facile et m'aide à suivre les clients et leurs dettes. Maintenant j'ai une meilleure organisation dans ma boutique."
                </p>
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                    <User className="w-5 h-5 text-green-600" />
                  </div>
                  <div>
                    <p className="font-semibold text-gray-900">أحمد - Ahmed</p>
                    <p className="text-sm text-gray-500">صاحب حانوت، الرباط</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Testimonial 2 */}
            <Card className="border-0 shadow-lg bg-white">
              <CardContent className="p-6">
                <div className="flex items-center gap-1 mb-4">
                  {[...Array(5)].map((_, i) => (
                    <Star key={i} className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                  ))}
                </div>
                <p className="text-gray-700 mb-4 leading-relaxed">
                  "كنت كانكتب كلشي في الكراسة، دابا ولا كلشي في التيليفون. واخا ماعرفتش التكنولوجيا مزيان، لقيت النظام بسيط."
                </p>
                <p className="text-sm text-gray-500 mb-4">
                  "J'écrivais tout dans un cahier, maintenant tout est sur le téléphone. Même si je ne connais pas bien la technologie, j'ai trouvé le système simple."
                </p>
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 bg-red-100 rounded-full flex items-center justify-center">
                    <User className="w-5 h-5 text-red-600" />
                  </div>
                  <div>
                    <p className="font-semibold text-gray-900">فاطمة - Fatima</p>
                    <p className="text-sm text-gray-500">صاحبة محل، الدار البيضاء</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Testimonial 3 */}
            <Card className="border-0 shadow-lg bg-white">
              <CardContent className="p-6">
                <div className="flex items-center gap-1 mb-4">
                  {[...Array(5)].map((_, i) => (
                    <Star key={i} className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                  ))}
                </div>
                <p className="text-gray-700 mb-4 leading-relaxed">
                  "الإحصائيات كاتعاونني نعرف أشنو المنتوجات اللي كاتبيع أكثر. دابا كانشري بذكاء أكثر."
                </p>
                <p className="text-sm text-gray-500 mb-4">
                  "Les statistiques m'aident à savoir quels produits se vendent le plus. Maintenant j'achète plus intelligemment."
                </p>
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                    <User className="w-5 h-5 text-blue-600" />
                  </div>
                  <div>
                    <p className="font-semibold text-gray-900">يوسف - Youssef</p>
                    <p className="text-sm text-gray-500">صاحب محل أدوات، فاس</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Final CTA Section */}
      <section className="py-20 bg-gradient-to-br from-green-600 to-red-600">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="space-y-8">
            <div>
              <h2 className="text-3xl md:text-5xl font-bold text-white mb-4">
                <span className="block">ابدا اليوم - مجاني وبسيط</span>
                <span className="block text-2xl md:text-4xl mt-2">
                  Commencez aujourd'hui – Gratuit et simple
                </span>
              </h2>
              <p className="text-xl text-green-100 max-w-2xl mx-auto">
                انضم لآلاف التجار المغاربة الذين يستخدمون نظامنا لإدارة محلاتهم
                <br />
                Rejoignez des milliers de commerçants marocains
              </p>
            </div>

            {/* Signup Form */}
            <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 max-w-2xl mx-auto">
              <form onSubmit={handleFormSubmit} className="space-y-6">
                <div className="grid md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <label className="text-white font-medium flex items-center gap-2">
                      <User className="w-4 h-4" />
                      الاسم / Nom
                    </label>
                    <Input
                      type="text"
                      placeholder="اسمك الكامل / Votre nom complet"
                      value={formData.name}
                      onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                      className="bg-white/90 border-0 h-12 text-gray-900 placeholder:text-gray-500"
                      required
                    />
                  </div>
                  <div className="space-y-2">
                    <label className="text-white font-medium flex items-center gap-2">
                      <Phone className="w-4 h-4" />
                      الهاتف / Téléphone
                    </label>
                    <Input
                      type="tel"
                      placeholder="06 XX XX XX XX"
                      value={formData.phone}
                      onChange={(e) => setFormData({ ...formData, phone: e.target.value })}
                      className="bg-white/90 border-0 h-12 text-gray-900 placeholder:text-gray-500"
                      required
                    />
                  </div>
                </div>
                <div className="space-y-2">
                  <label className="text-white font-medium flex items-center gap-2">
                    <Mail className="w-4 h-4" />
                    البريد الإلكتروني / Email
                  </label>
                  <Input
                    type="email"
                    placeholder="<EMAIL>"
                    value={formData.email}
                    onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                    className="bg-white/90 border-0 h-12 text-gray-900 placeholder:text-gray-500"
                    required
                  />
                </div>
                <Button
                  type="submit"
                  size="lg"
                  className="w-full bg-white text-green-600 hover:bg-gray-100 font-bold py-4 text-lg rounded-xl shadow-lg hover:shadow-xl transition-all duration-200"
                >
                  ابدا الآن مجانا / Commencer gratuitement
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Button>
              </form>

              <div className="mt-6 text-center">
                <p className="text-green-100 text-sm">
                  عندك حساب؟ / Vous avez déjà un compte ?{' '}
                  <button
                    onClick={handleLogin}
                    className="text-white font-semibold underline hover:no-underline"
                  >
                    دخل هنا / Connectez-vous ici
                  </button>
                </p>
              </div>
            </div>

            {/* Final Trust Indicators */}
            <div className="flex flex-wrap justify-center items-center gap-8 text-green-100">
              <div className="flex items-center gap-2">
                <CheckCircle className="w-5 h-5" />
                <span>بدون رسوم خفية / Aucun frais caché</span>
              </div>
              <div className="flex items-center gap-2">
                <CheckCircle className="w-5 h-5" />
                <span>دعم باللغة العربية / Support en arabe</span>
              </div>
              <div className="flex items-center gap-2">
                <CheckCircle className="w-5 h-5" />
                <span>يعمل على جميع الأجهزة / Compatible tous appareils</span>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center space-y-6">
            <div className="flex items-center justify-center gap-3">
              <div className="w-12 h-12 bg-gradient-to-br from-green-600 to-red-600 rounded-lg flex items-center justify-center">
                <Store className="w-6 h-6 text-white" />
              </div>
              <div>
                <h3 className="text-2xl font-bold">CRM الحانوت</h3>
                <p className="text-sm text-gray-400">Mini-Market Manager</p>
              </div>
            </div>

            <p className="text-gray-400 max-w-2xl mx-auto">
              نظام إدارة مبسط ومجاني للمحلات التجارية المغربية
              <br />
              Système de gestion gratuit et simple pour les commerces marocains
            </p>

            <div className="border-t border-gray-800 pt-6">
              <p className="text-sm text-gray-500">
                © 2024 CRM الحانوت. جميع الحقوق محفوظة / Tous droits réservés
              </p>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}
