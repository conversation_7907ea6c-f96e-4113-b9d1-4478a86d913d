# Fix Database Indexes for Phone/Email Registration

## Problem
You're getting this error when trying to register with phone only:
```
E11000 duplicate key error collection: crm-hanout.users index: email_1 dup key: { email: null }
```

This happens because the existing database has a non-sparse unique index on the email field, which doesn't allow multiple null values.

## Solution Options

### Option 1: Quick Fix via MongoDB Shell (Recommended)

1. **Connect to your MongoDB database** using MongoDB Compass, Studio 3T, or mongo shell

2. **Run these commands** in the MongoDB shell:

```javascript
// Switch to your database
use crm-hanout

// Drop the old non-sparse email index
db.users.dropIndex("email_1")

// Drop the old non-sparse phone index (if it exists)
db.users.dropIndex("phone_1")

// Create new sparse unique indexes
db.users.createIndex({ "email": 1 }, { "unique": true, "sparse": true, "name": "email_sparse_unique" })
db.users.createIndex({ "phone": 1 }, { "unique": true, "sparse": true, "name": "phone_sparse_unique" })

// Verify the indexes
db.users.getIndexes()
```

### Option 2: Using the Migration Script

1. **Install MongoDB driver** (if not already installed):
```bash
npm install mongodb
```

2. **Set your MongoDB URI** environment variable:
```bash
# Windows
set MONGODB_URI=mongodb://localhost:27017/crm-hanout

# Linux/Mac
export MONGODB_URI=mongodb://localhost:27017/crm-hanout
```

3. **Run the migration script**:
```bash
node scripts/fix-user-indexes.js
```

### Option 3: Manual Database Cleanup

If you're in development and don't have important user data:

1. **Drop the entire users collection**:
```javascript
use crm-hanout
db.users.drop()
```

2. **Restart your application** - the new indexes will be created automatically

## Verification

After applying the fix, you should be able to:

1. ✅ Register with email only
2. ✅ Register with phone only  
3. ✅ Register with both email and phone
4. ✅ Get proper validation errors for duplicates
5. ✅ Login with either email or phone

## Test the Fix

Try registering a new user with phone only:
- Phone: `0612345678`
- Password: `test123`
- Language: Arabic

The registration should now work without the duplicate key error.

## What Changed

### Before:
- Email field had `unique: true` without `sparse: true`
- Multiple null values caused duplicate key errors

### After:
- Email field has `unique: true, sparse: true`
- Phone field has `unique: true, sparse: true`
- Null values are ignored for uniqueness checks
- Only non-null values must be unique

## Database Schema

The new User schema supports:

```javascript
{
  email: String | null,     // Optional, unique when present
  phone: String | null,     // Optional, unique when present  
  hashedPassword: String,   // Required
  preferredLanguage: String // 'fr' or 'ar'
}
```

With validation ensuring at least one of email or phone is provided.

## Troubleshooting

### If you still get errors:

1. **Check your MongoDB connection string** in your environment variables
2. **Verify the database name** matches your application
3. **Ensure you have write permissions** on the database
4. **Try restarting your Next.js application** after fixing indexes

### If using MongoDB Atlas:

1. Use the **MongoDB Compass** or **Atlas web interface**
2. Navigate to your **crm-hanout** database
3. Go to the **users** collection
4. Click on **Indexes** tab
5. **Delete** the old `email_1` index
6. **Create** new sparse unique indexes as shown above

The fix ensures your CRM can handle the flexible registration system for Moroccan users who prefer using phone numbers over email addresses.
